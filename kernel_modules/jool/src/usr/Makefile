#
# gpl/usr/jool/src/usr/Makefile
#
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your 
# use of this file is subject to the CradlePoint Software License Agreement 
# distributed with this file. Unauthorized reproduction or distribution of 
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../../../..)
include $(ROOT)/Makefile.config

SUBS := argp nl util iptables joold nat64 siit
SUBS_CLEAN = $(patsubst %,%-clean, $(SUBS))

default: all 

all: $(SUBS)

install: $(SUBS)

argp: .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

nl: .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

util: .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

iptables: .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

joold: nl util .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

nat64: argp nl util .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

siit: argp nl util .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

clean: $(SUBS:=-clean)

$(SUBS_CLEAN): .PHONY
	$(MAKE) -C $(patsubst %-clean,%, $@) clean

.PHONY:
