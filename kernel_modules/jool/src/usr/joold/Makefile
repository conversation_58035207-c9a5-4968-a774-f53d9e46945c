#
# gpl/usr/jool/src/usr/joold/Makefile
#
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your 
# use of this file is subject to the CradlePoint Software License Agreement 
# distributed with this file. Unauthorized reproduction or distribution of 
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../../../../..)
include $(ROOT)/Makefile.config

CFLAGS  += -Wall -pedantic -std=gnu11 -O2 -I../..
CFLAGS += -DPACKAGE_NAME=\"Jool\" -DPACKAGE_TARNAME=\"jool\" -DPACKAGE_VERSION=\"4.1.7\" -DPACKAGE_STRING=\"Jool\ 4.1.7\" -DPACKAGE_BUGREPORT=\"<EMAIL>\" -DPACKAGE_URL=\"\" -DPACKAGE=\"jool\" -DVERSION=\"4.1.7\" -DSTDC_HEADERS=1 -DHAVE_SYS_TYPES_H=1 -DHAVE_SYS_STAT_H=1 -DHAVE_STDLIB_H=1 -DHAVE_STRING_H=1 -DHAVE_MEMORY_H=1 -DHAVE_STRINGS_H=1 -DHAVE_INTTYPES_H=1 -DHAVE_STDINT_H=1 -DHAVE_ARPA_INET_H=1 -DHAVE_UNISTD_H=1 -DHAVE__BOOL=1 -DHAVE_INET_NTOA=1 -DHAVE_MEMSET=1 -DHAVE_STRCASECMP=1 -DHAVE_STRTOL=1 -DHAVE_DLFCN_H=1

JOOL_OBJS := \
	joold.o \
	log.o \
	modsocket.o \
	netsocket.o

LIBS := -lpthread -lm -lnl -lnl-genl ../nl/libjoolnl.a ../util/libjoolutil.a

JOOL := joold

all: $(JOOL)

$(JOOL): ${JOOL_OBJS}
	$(CC) $(CFLAGS) $(LDFLAGS) -o $(JOOL) $(JOOL_OBJS) $(LIBS)

install: all
	mkdir -p $(DESTDIR)/usr/sbin
	rsync $(JOOL) $(DESTDIR)/usr/sbin

clean:
	rm -f *.o
	rm -f $(JOOL)

.PHONY: joold
