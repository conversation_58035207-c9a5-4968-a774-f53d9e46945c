.\" Manpage for jool's session synchronization daemon.

.TH joold 8 2022-01-27 v4.1.7 "NAT64 Jool's Session Synchronization Daemon"

.SH NAME
joold - Userspace Session Synchronization (SS) daemon for NAT64 Jool.

.SH DESCRIPTION
Opens two sockets: A Netlink socket to the current namespace's NAT64 Jool instance, and a network TCP socket to other/remote joold instances. Exchanges sessions indiscriminately between them.
.P
The intent is to keep the NAT64 session databases synchronized for the sake of having backup fallback NAT64s.
.P
The setup is strongly recommended to be Active/Passive; Active/Active is technically allowed, but not very reliable.
.P
Running the daemons is not enough to have successful session synchronization; SS needs to be enabled in the kernel instances as well. (See the --ss-* flags described in jool(8).)

.SH AVAILABILITY
Linux is the only OS in which this program makes sense.
.br
Kernels 4.4 and up.

.SH SYNTAX
.RI "joold [" FILE "]"

.SH OPTIONS
.IP FILE
Path to JSON file containing the network socket's configuration.
.br
Defaults to "netsocket.json".
.br
(The Netlink socket currently does not need configuration.)

.SH NETWORK SOCKET CONFIGURATION
The file is a JSON-formatted collection of keyvalues.

.IP "multicast address=<IPv6-or-IPv4-address>"
IP Address the SS traffic will be sent to and listened from.
.br
Mandatory; has no default.

.IP "multicast port=<port-or-service-name>"
TCP port where the SS traffic will be sent to and listened from.
.br
Mandatory; has no default.

.IP "in interface=<String>"
If multicast address is IPv4, this should be one addresses from the interface where the SS traffic is expected to be received. If multicast address is IPv6, this should be the name of the interface (eg. "eth0").
.br
Optional. Defaults to be automatically assigned by the kernel.
.br
It is strongly recommended that you define this value to ensure the SS traffic does not leak through other interfaces.

.IP "out interface=<String>"
If multicast address is IPv4, this should be one addresses from the interface where the multicast traffic is expected to be sent. If multicast address is IPv6, this should be the name of the interface (eg. "eth0").
.br
Optional. Defaults to be automatically assigned by the kernel.
.br
It is strongly recommended that you define this value to ensure the SS traffic does not leak through other interfaces.

.IP reuseaddr=<INT>
Please always include and enable this.

.IP ttl=<INT>
Time-to-live of packets sent out by this socket.

.SH EXAMPLES
IPv6 version:
.P
	$ # All Jool instances should have the basic configuration.
.br
	$ jool --file same-config-as-everyone-else.json
.br
	$
.br
	$ # Configure the SS interface
.br
	$ ip addr add 2001:db8:ff08::4/96 dev eth0
.br
	$ ip link set eth0 up
.br
	$
.br
	$ # joold
.br
	$ cat ipv6-netsocket.json
.br
	{
.br
		"multicast address": "ff08::db8:64:64",
.br
		"multicast port": "6464",
.br
		"in interface": "eth0",
.br
		"out interface": "eth0",
.br
		"reuseaddr": 1,
.br
		"ttl": 3
.br
	}
.br
	$ joold ipv6-netsocket.json &
.P
IPv4 version:
.P
	$ # All Jool instances should have the basic configuration.
.br
	$ jool --file same-config-as-everyone-else.json
.br
	$
.br
	$ # Configure the SS interface
.br
	$ ip addr add *********/24 dev eth0
.br
	$ ip link set eth0 up
.br
	$
.br
	$ # joold
.br
	$ cat ipv4-netsocket.json
.br
	{
.br
		"multicast address": "************",
.br
		"multicast port": "6464",
.br
		"in interface": "*********",
.br
		"out interface": "*********",
.br
		"reuseaddr": 1,
.br
		"ttl": 3
.br
	}
.br
	$ joold ipv4-netsocket.json &

.SH EXIT STATUS
Zero on success, non-zero on failure.

.SH AUTHOR
NIC Mexico & ITESM

.SH REPORTING BUGS
Our issue tracker is https://github.com/NICMx/Jool/issues.
.br
If you want to mail us instead, use <EMAIL>.

.SH KNOWN BUGS
1. reuseaddr should probably default to 1...
.br
2. Maybe the daemon should be able to automatically enable SS on the kernel module.
.P
To be perfectly honest, the main reason why fixing this isn't in the radar is because joold is giving me the impression that nobody is using it.

.SH COPYRIGHT
Copyright 2022 NIC Mexico.
.br
License: GPLv2 (GNU GPL version 2)
.br
This is free software: you are free to change and redistribute it.
There is NO WARRANTY, to the extent permitted by law.

.SH SEE ALSO
https://jool.mx/en/session-synchronization.html
https://jool.mx/en/config-joold.html
https://jool.mx/en/usr-flags-joold.html

