#
# gpl/usr/jool/src/mod/Makefile
#
# Copyright (c) 2022 CradlePoint, Inc. <www.cradlepoint.com>.
# All rights reserved.
#
# This file contains confidential information of CradlePoint, Inc. and your
# use of this file is subject to the CradlePoint Software License Agreement
# distributed with this file. Unauthorized reproduction or distribution of
# this file is subject to civil and criminal penalties.
#

ROOT ?= $(abspath ../../../..)
include $(ROOT)/Makefile.config

SUBS = common siit nat64

SUBS_CLEAN = $(patsubst %,%-clean, $(SUBS))

default: all

all: $(SUBS)

install: $(SUBS)

clean: $(SUBS:=-clean)

$(SUBS): .PHONY
	$(MAKE) -C $@ -j$(JOBS)
	$(MAKE) -C $@ install

$(SUBS_CLEAN): .PHONY
	$(MAKE) -C $(patsubst %-clean,%, $@) clean

.PHONY:


#PROJECTS = common siit nat64
#OTHER_TARGETS = modules modules_install clean debug


#all: $(PROJECTS)
	# Running the dependencies is enough.
#$(PROJECTS):
#	$(MAKE) -C $@
#$(OTHER_TARGETS):
#	$(foreach dir, $(PROJECTS), $(MAKE) -C $(dir) $@;)
#install:
	# Don't want to execute depmod thrice; it takes too long.
#	$(foreach dir, $(PROJECTS), $(MAKE) -C $(dir) modules_install;)
#	/sbin/depmod

# This target is needed to generate the upstream tarball.
# It's not a standard Kbuild target.
#distdir:
#	mkdir -p ${distdir}
#	cp -r * ${distdir}

#.PHONY: $(PROJECTS) $(OTHER_TARGETS) install dist
