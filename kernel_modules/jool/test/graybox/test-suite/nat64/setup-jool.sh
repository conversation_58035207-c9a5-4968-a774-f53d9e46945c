#!/bin/sh

modprobe -rq jool_siit
modprobe -rq jool
ip addr flush dev to_client_v6 scope global
ip addr flush dev to_client_v4 scope global
ip link set to_client_v6 up
ip link set to_client_v4 up

ip addr add 2001:db8::1/96 dev to_client_v6
ip addr add *********/24 dev to_client_v4
ip route add ***********/24 via *********

sysctl -w net.ipv4.conf.all.forwarding=1     > /dev/null
sysctl -w net.ipv6.conf.all.forwarding=1     > /dev/null
modprobe jool
jool instance add -6 64:ff9b::/96 --iptables
jool pool4 add ********* 1-3000 --tcp
jool pool4 add ********* 1-3000 --udp
jool pool4 add ********* 1-3000 --icmp
jool bib add *********#2000 2001:db8::5#2000 --tcp
jool bib add *********#2000 2001:db8::5#2000 --udp
jool bib add *********#1    2001:db8::5#1    --icmp

ip6tables -t mangle -A PREROUTING -d 64:ff9b::/96                       -j JOOL
iptables  -t mangle -A PREROUTING -d *********    -p tcp --dport 1:3000 -j JOOL
iptables  -t mangle -A PREROUTING -d *********    -p udp --dport 1:3000 -j JOOL
iptables  -t mangle -A PREROUTING -d *********    -p icmp               -j JOOL

# Relevant whenever the kernel responds an ICMPv6 error on behalf of Jool.
sysctl -w net.ipv6.auto_flowlabels=0 > /dev/null

# ptb64, ptb66 tests
jool bib add *********#1000 2001:db8:1::5#1001 --udp

# ptb66 test
ip route add 2001:db8:1::/96 via 2001:db8::5
jool bib add *********#1002 2001:db8::5#1003   --udp
