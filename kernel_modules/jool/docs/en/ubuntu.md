---
language: en
layout: default
category: Documentation
title: Ubuntu
---

[Documentation](documentation.html) > [Installation](documentation.html#installation) > Ubuntu

# Jool in Ubuntu (20.04+)

At time of writing, this installs Jool 4.0.7:

	sudo apt install jool-dkms jool-tools

It's always a bit outdated. If you prefer a newer version, consider using the [standalone Debian packages](debian.html#standalone-package) instead. You can also [compile from source](install.html).

Whichever method you chose, if you installed via `apt`, you can uninstall as usual:

	sudo apt remove jool-dkms jool-tools

