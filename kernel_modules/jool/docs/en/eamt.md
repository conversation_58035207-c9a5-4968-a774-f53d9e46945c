---
language: en
layout: default
category: Documentation
title: EAMT
---

[Documentation](documentation.html) > [SIIT in Detail](documentation.html#siit-in-detail) > The EAMT

# EAMT

The _Explicit Address Mappings Table_ (EAMT) is a collection of records in an SIIT device which describe how different addresses should be translated. It is defined in [RFC 7757]({{ site.draft-siit-eam }}).

Here's an example of an EAMT:

| IPv4 Prefix     |     IPv6 Prefix      |
|-----------------|----------------------|
| *********/32    | 2001:db8:aaaa::5/128 |
| ************/24 | 2001:db8:bbbb::/120  |
| ***********/29  | 2001:db8:cccc::/125  |

An EAMT entry is composed of an IPv4 prefix and an IPv6 prefix. When an address is being translated, its prefix is literally replaced according to the table. In <PERSON><PERSON>'s implementation, all records are bidirectional.

The first record shown is the simplest case. Because there is no suffix, the record literally says "Address `*********` should always be translated as `2001:db8:aaaa::5`, and vice versa". Whether the address is source, destination, or lies within the inner packet of an ICMP error, doesn't matter. The IPv6 representation of `*********` is `2001:db8:aaaa::5`, and the IPv4 representation of `2001:db8:aaaa::5` is `*********`. Period.

The second entry is more interesting. Because there is a full byte of suffix, the record is saying "`198.51.100.x` should be translated as `2001:db8:bbbb::x`, and vice versa." _x_ is any number whose decimal representation lies between 0 and 255.

As in:

- `************` <-> `2001:db8:bbbb::0`
- `************` <-> `2001:db8:bbbb::1`
- `************` <-> `2001:db8:bbbb::2`
- ...
- `************54` <-> `2001:db8:bbbb::fe`
- `************55` <-> `2001:db8:bbbb::ff`

This form can help you simplify configuration when you have lots of addresses to map; the suffix is always preserved, so the point is a single EAMT entry can describe the translation of an entire network.

(Also, a single EAMT entry describing a /16 is way more efficient than the equivalent 65536 atomic records.)

The prefix replacement is done at bit level. The third entry exemplifies this: Address `***********` becomes `2001:db8:cccc::`, not `2001:db8:cccc::8`. This is because `.8`'s binary form is `00001000`, and the one is at the prefix side. These are some other mappings generated by the entry:

- `***********` <-> `2001:db8:cccc::1`
- `************` <-> `2001:db8:cccc::2`
- `************` <-> `2001:db8:cccc::4`
- `************` <-> `2001:db8:cccc::7`

If Jool doesn't find a match in the table for an address, it tries to translate based on the [`pool6`](usr-flags-pool6.html) prefix. If that also fails, the packet is returned to the kernel (ie. it is assumed it wasn't meant to be translated).

See the [walkthrough](run-eam.html) or the [reference material](usr-flags-eamt.html) for information on how to create and destroy entries manually. You can test the translation of an IP address given a certain EAMT by way of [`address query`](usr-flags-address.html).
